################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Add inputs and outputs from these tool invocations to the build variables 
SYSCFG_SRCS += \
../empty.syscfg 

C_SRCS += \
../empty.c \
./ti_msp_dl_config.c \
C:/ti/mspm0_sdk_2_05_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c \
../servo_50hz_test.c \
../servo_compile_test.c \
../servo_example.c \
../servo_test.c 

GEN_CMDS += \
./device_linker.cmd 

GEN_FILES += \
./device_linker.cmd \
./device.opt \
./ti_msp_dl_config.c 

C_DEPS += \
./empty.d \
./ti_msp_dl_config.d \
./startup_mspm0g350x_ticlang.d \
./servo_50hz_test.d \
./servo_compile_test.d \
./servo_example.d \
./servo_test.d 

GEN_OPTS += \
./device.opt 

OBJS += \
./empty.o \
./ti_msp_dl_config.o \
./startup_mspm0g350x_ticlang.o \
./servo_50hz_test.o \
./servo_compile_test.o \
./servo_example.o \
./servo_test.o 

GEN_MISC_FILES += \
./device.cmd.genlibs \
./ti_msp_dl_config.h \
./Event.dot 

OBJS__QUOTED += \
"empty.o" \
"ti_msp_dl_config.o" \
"startup_mspm0g350x_ticlang.o" \
"servo_50hz_test.o" \
"servo_compile_test.o" \
"servo_example.o" \
"servo_test.o" 

GEN_MISC_FILES__QUOTED += \
"device.cmd.genlibs" \
"ti_msp_dl_config.h" \
"Event.dot" 

C_DEPS__QUOTED += \
"empty.d" \
"ti_msp_dl_config.d" \
"startup_mspm0g350x_ticlang.d" \
"servo_50hz_test.d" \
"servo_compile_test.d" \
"servo_example.d" \
"servo_test.d" 

GEN_FILES__QUOTED += \
"device_linker.cmd" \
"device.opt" \
"ti_msp_dl_config.c" 

C_SRCS__QUOTED += \
"../empty.c" \
"./ti_msp_dl_config.c" \
"C:/ti/mspm0_sdk_2_05_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c" \
"../servo_50hz_test.c" \
"../servo_compile_test.c" \
"../servo_example.c" \
"../servo_test.c" 

SYSCFG_SRCS__QUOTED += \
"../empty.syscfg" 


