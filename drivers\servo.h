//
// Created by AI Assistant
// 舵机控制库头文件
//

#ifndef SERVO_H
#define SERVO_H

#include "common_inc.h"

// 舵机类型定义
typedef enum {
    SERVO_180_DEGREE = 0,  // 180度舵机
    SERVO_270_DEGREE = 1   // 270度舵机
} servo_type_t;

// 舵机通道定义
typedef enum {
    SERVO_CHANNEL_1 = 0,   // 使用TIMG6 CC0 (PB2)
    SERVO_CHANNEL_2 = 1    // 使用TIMG6 CC1 (PA22)
} servo_channel_t;

// 舵机配置结构体
typedef struct {
    servo_type_t type;          // 舵机类型
    servo_channel_t channel;    // 舵机通道
    uint16_t min_pulse;         // 最小脉宽 (单位: 定时器计数值)
    uint16_t max_pulse;         // 最大脉宽 (单位: 定时器计数值)
    uint16_t center_pulse;      // 中心脉宽 (单位: 定时器计数值)
    float current_angle;        // 当前角度
} servo_config_t;

// 舵机初始化函数
void servo_init(void);

// 设置舵机角度
// channel: 舵机通道 (SERVO_CHANNEL_1 或 SERVO_CHANNEL_2)
// angle: 目标角度 (180度舵机: 0-180度, 270度舵机: 0-270度)
void servo_set_angle(servo_channel_t channel, float angle);

// 设置舵机脉宽 (微秒)
// channel: 舵机通道
// pulse_us: 脉宽 (单位: 微秒, 通常500-2500us)
void servo_set_pulse_us(servo_channel_t channel, uint16_t pulse_us);

// 配置舵机类型
// channel: 舵机通道
// type: 舵机类型 (SERVO_180_DEGREE 或 SERVO_270_DEGREE)
void servo_config_type(servo_channel_t channel, servo_type_t type);

// 获取当前角度
float servo_get_angle(servo_channel_t channel);

// 舵机停止 (设置为中心位置)
void servo_stop(servo_channel_t channel);

// 停止所有舵机
void servo_stop_all(void);

// 舵机测试函数
void servo_test_180_degree(servo_channel_t channel);
void servo_test_270_degree(servo_channel_t channel);
void servo_test_both_servos(void);

#endif //SERVO_H
