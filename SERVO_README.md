# 舵机控制库使用说明

## 概述
本舵机库基于MSPM0G3507的PWM功能，复用现有的电机PWM引脚，支持控制两个舵机（一个180度，一个270度）。

## 硬件连接
- **舵机1 (180度)**: 连接到 PB2 引脚 (TIMG6 CC0)
- **舵机2 (270度)**: 连接到 PA22 引脚 (TIMG6 CC1)

## 文件结构
```
drivers/
├── servo.h          # 舵机库头文件
├── servo.c          # 舵机库实现文件
servo_test.c         # 完整测试程序
servo_example.c      # 使用示例
SERVO_README.md      # 本说明文件
```

## 主要功能

### 1. 初始化
```c
#include "servo.h"

// 初始化舵机系统
servo_init();
```

### 2. 配置舵机类型
```c
// 配置通道1为180度舵机
servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);

// 配置通道2为270度舵机
servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
```

### 3. 角度控制
```c
// 设置180度舵机到90度位置
servo_set_angle(SERVO_CHANNEL_1, 90.0f);

// 设置270度舵机到135度位置
servo_set_angle(SERVO_CHANNEL_2, 135.0f);
```

### 4. 脉宽控制
```c
// 直接设置脉宽（微秒）
servo_set_pulse_us(SERVO_CHANNEL_1, 1500);  // 1.5ms脉宽
```

### 5. 获取当前角度
```c
float current_angle = servo_get_angle(SERVO_CHANNEL_1);
```

### 6. 停止控制
```c
// 停止单个舵机（回到中心位置）
servo_stop(SERVO_CHANNEL_1);

// 停止所有舵机
servo_stop_all();
```

## 测试函数

### 1. 单个舵机测试
```c
// 测试180度舵机
servo_test_180_degree(SERVO_CHANNEL_1);

// 测试270度舵机
servo_test_270_degree(SERVO_CHANNEL_2);
```

### 2. 双舵机同步测试
```c
servo_test_both_servos();
```

## 技术参数

### PWM配置
- **频率**: 50Hz (20ms周期) - 舵机标准频率
- **脉宽范围**: 0.5ms - 2.5ms
- **分辨率**: 约0.78us (1.28MHz定时器时钟)
- **PWM周期**: 25600 计数值 (20ms @ 1.28MHz)

### 角度范围
- **180度舵机**: 0° - 180°
- **270度舵机**: 0° - 270°

### 脉宽对应关系
- **0.5ms**: 最小角度 (0°) - 640 计数值
- **1.5ms**: 中心角度 (90°/135°) - 1920 计数值
- **2.5ms**: 最大角度 (180°/270°) - 3200 计数值

### 重要说明
- 舵机初始化时会重新配置PWM6为标准50Hz频率
- 这会覆盖原有的电机PWM配置，因此舵机和电机不能同时使用
- 使用标准50Hz频率确保所有舵机都能稳定工作

## 使用示例

### 基本使用
```c
#include "common_inc.h"

int main(void)
{
    SYSCFG_DL_init();
    
    // 初始化舵机
    servo_init();
    
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    while (1) {
        // 控制舵机运动
        servo_set_angle(SERVO_CHANNEL_1, 0.0f);
        servo_set_angle(SERVO_CHANNEL_2, 0.0f);
        delay_ms(1000);
        
        servo_set_angle(SERVO_CHANNEL_1, 180.0f);
        servo_set_angle(SERVO_CHANNEL_2, 270.0f);
        delay_ms(1000);
    }
}
```

### 运行测试程序
```c
// 在main函数中调用
int main(void)
{
    return servo_test_main();  // 运行完整测试
}
```

## 编译和使用

### 编译步骤
1. 确保所有文件都已添加到项目中
2. `drivers/servo.h` 和 `drivers/servo.c` 已包含在编译中
3. `common_inc.h` 已包含 `servo.h`
4. 编译项目

### 快速开始
1. **启用舵机测试**: 在 `empty.c` 的 main 函数中，取消注释第130行：
   ```c
   servo_simple_test();  // 运行舵机测试，不会返回
   ```

2. **集成到现有代码**: 舵机已集成到主循环中，会自动进行测试

3. **自定义控制**: 使用API函数控制舵机：
   ```c
   servo_init();
   servo_set_angle(SERVO_CHANNEL_1, 90.0f);
   ```

## 注意事项

1. **电源要求**: 确保舵机有足够的电源供应（通常5V，电流根据舵机规格）
2. **PWM冲突**: 使用舵机时不能同时使用电机控制（共用PWM资源）
3. **频率兼容性**: 使用1000Hz PWM频率，大多数舵机兼容，如有问题请调整syscfg
4. **角度限制**:
   - 180度舵机: 0° - 180°
   - 270度舵机: 0° - 270°
   - 超出范围的角度会被自动限制
5. **脉宽范围**: 500us - 2500us，超出范围会被限制
6. **更新频率**: 建议控制更新频率不超过50Hz，避免舵机抖动

## 故障排除

### 舵机不动
1. 检查电源连接
2. 确认PWM引脚连接正确
3. 检查舵机是否损坏

### 舵机抖动
1. 降低控制更新频率
2. 检查电源是否稳定
3. 确认脉宽设置在有效范围内

### 角度不准确
1. 使用`servo_calibration()`函数校准
2. 调整脉宽范围参数
3. 检查舵机机械限位

## 扩展功能

如需扩展更多舵机，可以：
1. 使用TIMG7的PWM通道（PA28, PA24）
2. 修改库文件支持4通道舵机控制
3. 添加软件PWM支持更多通道

## 版本信息
- 版本: 1.0
- 作者: AI Assistant
- 支持芯片: MSPM0G3507
- 测试日期: 2024
