//
// Created by AI Assistant
// 舵机控制库实现文件
//

#include "servo.h"

// 舵机配置数组
static servo_config_t servo_configs[2];

// 舵机标准50Hz PWM配置
// 系统时钟: 6.4MHz, 分频: 5, 所以定时器时钟: 6.4MHz/5 = 1.28MHz
// 50Hz周期: 20ms = 1.28MHz * 0.02s = 25600 计数值
#define SERVO_PWM_PERIOD    25600

// 脉宽转换宏 (微秒转定时器计数值)
// 1us = 1.28 计数值
#define US_TO_COUNTS(us)    ((us) * 128 / 100)

// 默认脉宽定义 (微秒)
#define SERVO_MIN_PULSE_US      500     // 0.5ms
#define SERVO_MAX_PULSE_US_180  2500    // 2.5ms (180度)
#define SERVO_MAX_PULSE_US_270  2500    // 2.5ms (270度，可能需要调整)
#define SERVO_CENTER_PULSE_US   1500    // 1.5ms

/**
 * @brief 舵机初始化 - 重新配置PWM为50Hz
 */
void servo_init(void)
{
    // 停止定时器
    DL_TimerG_stopCounter(PWM_6_INST);

    // 重新配置PWM为舵机标准50Hz
    // 获取现有的时钟配置
    static const DL_TimerG_ClockConfig servo_clock_config = {
        .clockSel = DL_TIMER_CLOCK_BUSCLK,
        .divideRatio = DL_TIMER_CLOCK_DIVIDE_5,
        .prescale = 0U
    };

    // 配置50Hz PWM
    static const DL_TimerG_PWMConfig servo_pwm_config = {
        .pwmMode = DL_TIMER_PWM_MODE_EDGE_ALIGN_UP,
        .period = SERVO_PWM_PERIOD,  // 25600 for 50Hz
        .isTimerWithFourCC = false,
        .startTimer = DL_TIMER_STOP,
    };

    // 重新设置时钟配置
    DL_TimerG_setClockConfig(PWM_6_INST, (DL_TimerG_ClockConfig *) &servo_clock_config);

    // 重新初始化PWM模式
    DL_TimerG_initPWMMode(PWM_6_INST, (DL_TimerG_PWMConfig *) &servo_pwm_config);

    // 重新配置输出控制
    DL_TimerG_setCaptureCompareOutCtl(PWM_6_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
        DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
        DL_TIMERG_CAPTURE_COMPARE_0_INDEX);

    DL_TimerG_setCaptureCompareOutCtl(PWM_6_INST, DL_TIMER_CC_OCTL_INIT_VAL_LOW,
        DL_TIMER_CC_OCTL_INV_OUT_DISABLED, DL_TIMER_CC_OCTL_SRC_FUNCVAL,
        DL_TIMERG_CAPTURE_COMPARE_1_INDEX);

    // 设置更新方法
    DL_TimerG_setCaptCompUpdateMethod(PWM_6_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE,
        DL_TIMERG_CAPTURE_COMPARE_0_INDEX);
    DL_TimerG_setCaptCompUpdateMethod(PWM_6_INST, DL_TIMER_CC_UPDATE_METHOD_IMMEDIATE,
        DL_TIMERG_CAPTURE_COMPARE_1_INDEX);

    // 初始化舵机配置
    // 通道1 - 默认180度舵机
    servo_configs[SERVO_CHANNEL_1].type = SERVO_180_DEGREE;
    servo_configs[SERVO_CHANNEL_1].channel = SERVO_CHANNEL_1;
    servo_configs[SERVO_CHANNEL_1].min_pulse = US_TO_COUNTS(SERVO_MIN_PULSE_US);
    servo_configs[SERVO_CHANNEL_1].max_pulse = US_TO_COUNTS(SERVO_MAX_PULSE_US_180);
    servo_configs[SERVO_CHANNEL_1].center_pulse = US_TO_COUNTS(SERVO_CENTER_PULSE_US);
    servo_configs[SERVO_CHANNEL_1].current_angle = 90.0f;

    // 通道2 - 默认270度舵机
    servo_configs[SERVO_CHANNEL_2].type = SERVO_270_DEGREE;
    servo_configs[SERVO_CHANNEL_2].channel = SERVO_CHANNEL_2;
    servo_configs[SERVO_CHANNEL_2].min_pulse = US_TO_COUNTS(SERVO_MIN_PULSE_US);
    servo_configs[SERVO_CHANNEL_2].max_pulse = US_TO_COUNTS(SERVO_MAX_PULSE_US_270);
    servo_configs[SERVO_CHANNEL_2].center_pulse = US_TO_COUNTS(SERVO_CENTER_PULSE_US);
    servo_configs[SERVO_CHANNEL_2].current_angle = 135.0f;

    // 设置初始位置为中心
    DL_TimerG_setCaptureCompareValue(PWM_6_INST,
        servo_configs[SERVO_CHANNEL_1].center_pulse, DL_TIMER_CC_0_INDEX);
    DL_TimerG_setCaptureCompareValue(PWM_6_INST,
        servo_configs[SERVO_CHANNEL_2].center_pulse, DL_TIMER_CC_1_INDEX);

    // 启用时钟并启动定时器
    DL_TimerG_enableClock(PWM_6_INST);
    DL_TimerG_startCounter(PWM_6_INST);
}

/**
 * @brief 设置舵机角度
 * @param channel 舵机通道
 * @param angle 目标角度
 */
void servo_set_angle(servo_channel_t channel, float angle)
{
    if (channel >= 2) return;
    
    servo_config_t *config = &servo_configs[channel];
    
    // 角度范围限制
    float max_angle = (config->type == SERVO_180_DEGREE) ? 180.0f : 270.0f;
    if (angle < 0.0f) angle = 0.0f;
    if (angle > max_angle) angle = max_angle;
    
    // 计算脉宽
    float pulse_range = config->max_pulse - config->min_pulse;
    uint16_t pulse_width = config->min_pulse + (uint16_t)(pulse_range * angle / max_angle);
    
    // 设置PWM占空比
    if (channel == SERVO_CHANNEL_1) {
        DL_TimerG_setCaptureCompareValue(PWM_6_INST, pulse_width, DL_TIMER_CC_0_INDEX);
    } else {
        DL_TimerG_setCaptureCompareValue(PWM_6_INST, pulse_width, DL_TIMER_CC_1_INDEX);
    }
    
    // 更新当前角度
    config->current_angle = angle;
}

/**
 * @brief 设置舵机脉宽 (微秒)
 * @param channel 舵机通道
 * @param pulse_us 脉宽 (微秒)
 */
void servo_set_pulse_us(servo_channel_t channel, uint16_t pulse_us)
{
    if (channel >= 2) return;
    
    // 脉宽范围限制
    if (pulse_us < 500) pulse_us = 500;
    if (pulse_us > 2500) pulse_us = 2500;
    
    uint16_t pulse_counts = US_TO_COUNTS(pulse_us);
    
    // 设置PWM占空比
    if (channel == SERVO_CHANNEL_1) {
        DL_TimerG_setCaptureCompareValue(PWM_6_INST, pulse_counts, DL_TIMER_CC_0_INDEX);
    } else {
        DL_TimerG_setCaptureCompareValue(PWM_6_INST, pulse_counts, DL_TIMER_CC_1_INDEX);
    }
}

/**
 * @brief 配置舵机类型
 * @param channel 舵机通道
 * @param type 舵机类型
 */
void servo_config_type(servo_channel_t channel, servo_type_t type)
{
    if (channel >= 2) return;
    
    servo_configs[channel].type = type;
    
    // 重新设置最大脉宽
    if (type == SERVO_180_DEGREE) {
        servo_configs[channel].max_pulse = US_TO_COUNTS(SERVO_MAX_PULSE_US_180);
        servo_configs[channel].current_angle = 90.0f;
    } else {
        servo_configs[channel].max_pulse = US_TO_COUNTS(SERVO_MAX_PULSE_US_270);
        servo_configs[channel].current_angle = 135.0f;
    }
}

/**
 * @brief 获取当前角度
 * @param channel 舵机通道
 * @return 当前角度
 */
float servo_get_angle(servo_channel_t channel)
{
    if (channel >= 2) return 0.0f;
    return servo_configs[channel].current_angle;
}

/**
 * @brief 舵机停止 (设置为中心位置)
 * @param channel 舵机通道
 */
void servo_stop(servo_channel_t channel)
{
    if (channel >= 2) return;
    
    servo_config_t *config = &servo_configs[channel];
    float center_angle = (config->type == SERVO_180_DEGREE) ? 90.0f : 135.0f;
    servo_set_angle(channel, center_angle);
}

/**
 * @brief 停止所有舵机
 */
void servo_stop_all(void)
{
    servo_stop(SERVO_CHANNEL_1);
    servo_stop(SERVO_CHANNEL_2);
}

/**
 * @brief 180度舵机测试函数
 * @param channel 舵机通道
 */
void servo_test_180_degree(servo_channel_t channel)
{
    // 确保配置为180度舵机
    servo_config_type(channel, SERVO_180_DEGREE);

    // 测试序列: 0° -> 90° -> 180° -> 90° -> 0°
    servo_set_angle(channel, 0.0f);
    delay_ms(1000);

    servo_set_angle(channel, 45.0f);
    delay_ms(1000);

    servo_set_angle(channel, 90.0f);
    delay_ms(1000);

    servo_set_angle(channel, 135.0f);
    delay_ms(1000);

    servo_set_angle(channel, 180.0f);
    delay_ms(1000);

    servo_set_angle(channel, 135.0f);
    delay_ms(1000);

    servo_set_angle(channel, 90.0f);
    delay_ms(1000);

    servo_set_angle(channel, 45.0f);
    delay_ms(1000);

    servo_set_angle(channel, 0.0f);
    delay_ms(1000);

    // 回到中心位置
    servo_set_angle(channel, 90.0f);
}

/**
 * @brief 270度舵机测试函数
 * @param channel 舵机通道
 */
void servo_test_270_degree(servo_channel_t channel)
{
    // 确保配置为270度舵机
    servo_config_type(channel, SERVO_270_DEGREE);

    // 测试序列: 0° -> 135° -> 270° -> 135° -> 0°
    servo_set_angle(channel, 0.0f);
    delay_ms(1000);

    servo_set_angle(channel, 67.5f);
    delay_ms(1000);

    servo_set_angle(channel, 135.0f);
    delay_ms(1000);

    servo_set_angle(channel, 202.5f);
    delay_ms(1000);

    servo_set_angle(channel, 270.0f);
    delay_ms(1000);

    servo_set_angle(channel, 202.5f);
    delay_ms(1000);

    servo_set_angle(channel, 135.0f);
    delay_ms(1000);

    servo_set_angle(channel, 67.5f);
    delay_ms(1000);

    servo_set_angle(channel, 0.0f);
    delay_ms(1000);

    // 回到中心位置
    servo_set_angle(channel, 135.0f);
}

/**
 * @brief 双舵机测试函数
 */
void servo_test_both_servos(void)
{
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);

    // 同步测试
    servo_set_angle(SERVO_CHANNEL_1, 0.0f);
    servo_set_angle(SERVO_CHANNEL_2, 0.0f);
    delay_ms(1500);

    servo_set_angle(SERVO_CHANNEL_1, 90.0f);
    servo_set_angle(SERVO_CHANNEL_2, 135.0f);
    delay_ms(1500);

    servo_set_angle(SERVO_CHANNEL_1, 180.0f);
    servo_set_angle(SERVO_CHANNEL_2, 270.0f);
    delay_ms(1500);

    servo_set_angle(SERVO_CHANNEL_1, 90.0f);
    servo_set_angle(SERVO_CHANNEL_2, 135.0f);
    delay_ms(1500);

    // 交替测试
    for (int i = 0; i < 3; i++) {
        servo_set_angle(SERVO_CHANNEL_1, 0.0f);
        servo_set_angle(SERVO_CHANNEL_2, 270.0f);
        delay_ms(1000);

        servo_set_angle(SERVO_CHANNEL_1, 180.0f);
        servo_set_angle(SERVO_CHANNEL_2, 0.0f);
        delay_ms(1000);
    }

    // 回到中心位置
    servo_set_angle(SERVO_CHANNEL_1, 90.0f);
    servo_set_angle(SERVO_CHANNEL_2, 135.0f);
}
