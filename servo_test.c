/*
 * 舵机测试程序
 * 演示如何使用舵机库控制180度和270度舵机
 */

#include "common_inc.h"

// 舵机测试主函数
int servo_test_main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化TFT180屏幕用于显示测试信息
    tft180_init();
    delay_ms(500);
    tft180_clear_color(BLACK);
    
    // 显示测试开始信息
    tft180_show_string_color(10, 10, "Servo Test Start", BLACK, GREEN);
    tft180_show_string_color(10, 30, "CH1: 180 Degree", BLACK, YELLOW);
    tft180_show_string_color(10, 50, "CH2: 270 Degree", BLACK, YELLOW);
    delay_ms(2000);
    
    // 初始化舵机
    servo_init();
    tft180_show_string_color(10, 70, "Servo Initialized", BLACK, GREEN);
    delay_ms(1000);
    
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);  // 通道1: 180度舵机
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);  // 通道2: 270度舵机
    
    while (1) {
        // 测试1: 180度舵机单独测试
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "Test 1: 180 Servo", BLACK, CYAN);
        tft180_show_string_color(10, 30, "Channel 1 (PB2)", BLACK, WHITE);
        servo_test_180_degree(SERVO_CHANNEL_1);
        delay_ms(1000);
        
        // 测试2: 270度舵机单独测试
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "Test 2: 270 Servo", BLACK, CYAN);
        tft180_show_string_color(10, 30, "Channel 2 (PA22)", BLACK, WHITE);
        servo_test_270_degree(SERVO_CHANNEL_2);
        delay_ms(1000);
        
        // 测试3: 双舵机同步测试
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "Test 3: Both Servo", BLACK, CYAN);
        tft180_show_string_color(10, 30, "Sync Movement", BLACK, WHITE);
        servo_test_both_servos();
        delay_ms(1000);
        
        // 测试4: 精确角度控制测试
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "Test 4: Precision", BLACK, CYAN);
        tft180_show_string_color(10, 30, "Angle Control", BLACK, WHITE);
        
        // 180度舵机精确角度测试
        for (int angle = 0; angle <= 180; angle += 30) {
            servo_set_angle(SERVO_CHANNEL_1, (float)angle);
            tft180_show_string_color(10, 50, "180° Servo:", BLACK, GREEN);
            tft180_show_int(100, 50, angle, 3);
            tft180_show_string_color(130, 50, "deg", BLACK, GREEN);
            delay_ms(800);
        }
        
        // 270度舵机精确角度测试
        for (int angle = 0; angle <= 270; angle += 45) {
            servo_set_angle(SERVO_CHANNEL_2, (float)angle);
            tft180_show_string_color(10, 70, "270° Servo:", BLACK, YELLOW);
            tft180_show_int(100, 70, angle, 3);
            tft180_show_string_color(130, 70, "deg", BLACK, YELLOW);
            delay_ms(800);
        }
        
        // 测试5: 脉宽控制测试
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "Test 5: Pulse Width", BLACK, CYAN);
        tft180_show_string_color(10, 30, "Control (us)", BLACK, WHITE);
        
        // 测试不同脉宽
        uint16_t pulse_widths[] = {500, 1000, 1500, 2000, 2500};
        for (int i = 0; i < 5; i++) {
            servo_set_pulse_us(SERVO_CHANNEL_1, pulse_widths[i]);
            servo_set_pulse_us(SERVO_CHANNEL_2, pulse_widths[i]);
            
            tft180_show_string_color(10, 50, "Pulse:", BLACK, GREEN);
            tft180_show_int(60, 50, pulse_widths[i], 4);
            tft180_show_string_color(100, 50, "us", BLACK, GREEN);
            delay_ms(1500);
        }
        
        // 回到中心位置
        servo_stop_all();
        
        // 测试完成，显示信息
        tft180_clear_color(BLACK);
        tft180_show_string_color(10, 10, "All Tests Complete", BLACK, GREEN);
        tft180_show_string_color(10, 30, "Restarting in 3s", BLACK, YELLOW);
        delay_ms(3000);
    }
    
    return 0;
}

// 简单的舵机控制演示函数
void servo_simple_demo(void)
{
    // 初始化舵机
    servo_init();
    
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    while (1) {
        // 简单的来回摆动
        servo_set_angle(SERVO_CHANNEL_1, 0.0f);
        servo_set_angle(SERVO_CHANNEL_2, 0.0f);
        delay_ms(1000);
        
        servo_set_angle(SERVO_CHANNEL_1, 180.0f);
        servo_set_angle(SERVO_CHANNEL_2, 270.0f);
        delay_ms(1000);
    }
}

// 舵机角度扫描函数
void servo_angle_sweep(servo_channel_t channel, servo_type_t type, uint16_t step_delay)
{
    servo_config_type(channel, type);
    
    float max_angle = (type == SERVO_180_DEGREE) ? 180.0f : 270.0f;
    float step = 5.0f;  // 5度步进
    
    // 正向扫描
    for (float angle = 0.0f; angle <= max_angle; angle += step) {
        servo_set_angle(channel, angle);
        delay_ms(step_delay);
    }
    
    // 反向扫描
    for (float angle = max_angle; angle >= 0.0f; angle -= step) {
        servo_set_angle(channel, angle);
        delay_ms(step_delay);
    }
    
    // 回到中心
    servo_set_angle(channel, max_angle / 2.0f);
}
