/*
 * 舵机使用示例
 * 展示如何在主程序中集成舵机控制
 */

#include "common_inc.h"

// 舵机控制示例主函数
int servo_example_main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    
    // 初始化屏幕
    tft180_init();
    delay_ms(500);
    tft180_clear_color(BLACK);
    tft180_show_string_color(10, 10, "Servo Example", BLACK, GREEN);
    
    // 初始化舵机
    servo_init();
    
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);  // 通道1: 180度舵机 (PB2)
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);  // 通道2: 270度舵机 (PA22)
    
    tft180_show_string_color(10, 30, "Servo Ready!", BLACK, YELLOW);
    delay_ms(1000);
    
    uint32_t counter = 0;
    
    while (1) {
        // 每秒更新一次舵机位置
        counter++;
        
        // 180度舵机：0-180度正弦波运动
        float angle_180 = 90.0f + 90.0f * sin(counter * 0.1f);
        servo_set_angle(SERVO_CHANNEL_1, angle_180);
        
        // 270度舵机：0-270度三角波运动
        float angle_270 = (counter % 100) * 2.7f;
        if ((counter / 100) % 2 == 1) {
            angle_270 = 270.0f - angle_270;
        }
        servo_set_angle(SERVO_CHANNEL_2, angle_270);
        
        // 显示当前角度
        tft180_show_string_color(10, 50, "180° Servo:", BLACK, GREEN);
        tft180_show_float(100, 50, angle_180, 3, 1);
        
        tft180_show_string_color(10, 70, "270° Servo:", BLACK, CYAN);
        tft180_show_float(100, 70, angle_270, 3, 1);
        
        delay_ms(100);  // 100ms更新间隔
    }
    
    return 0;
}

// 舵机手动控制示例（可以通过按键或其他输入控制）
void servo_manual_control_example(void)
{
    servo_init();
    
    // 配置舵机
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    float angle1 = 90.0f;   // 180度舵机当前角度
    float angle2 = 135.0f;  // 270度舵机当前角度
    
    while (1) {
        // 这里可以添加按键检测或其他输入方式来控制角度
        // 示例：简单的自动控制
        
        // 每5秒改变一次角度
        static uint32_t last_change = 0;
        if (DL_TimerG_getTimerCount(TIMER_8_INST) - last_change > 5000) {
            last_change = DL_TimerG_getTimerCount(TIMER_8_INST);
            
            // 随机角度（简单示例）
            angle1 = (angle1 == 90.0f) ? 45.0f : 90.0f;
            angle2 = (angle2 == 135.0f) ? 200.0f : 135.0f;
            
            servo_set_angle(SERVO_CHANNEL_1, angle1);
            servo_set_angle(SERVO_CHANNEL_2, angle2);
        }
        
        delay_ms(100);
    }
}

// 舵机校准函数
void servo_calibration(void)
{
    servo_init();
    
    tft180_clear_color(BLACK);
    tft180_show_string_color(10, 10, "Servo Calibration", BLACK, GREEN);
    
    // 校准180度舵机
    tft180_show_string_color(10, 30, "Cal 180° Servo...", BLACK, YELLOW);
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    
    // 测试最小位置
    servo_set_pulse_us(SERVO_CHANNEL_1, 500);
    tft180_show_string_color(10, 50, "Min: 500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 测试中心位置
    servo_set_pulse_us(SERVO_CHANNEL_1, 1500);
    tft180_show_string_color(10, 50, "Center: 1500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 测试最大位置
    servo_set_pulse_us(SERVO_CHANNEL_1, 2500);
    tft180_show_string_color(10, 50, "Max: 2500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 校准270度舵机
    tft180_show_string_color(10, 30, "Cal 270° Servo...", BLACK, YELLOW);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    // 测试最小位置
    servo_set_pulse_us(SERVO_CHANNEL_2, 500);
    tft180_show_string_color(10, 70, "Min: 500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 测试中心位置
    servo_set_pulse_us(SERVO_CHANNEL_2, 1500);
    tft180_show_string_color(10, 70, "Center: 1500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 测试最大位置
    servo_set_pulse_us(SERVO_CHANNEL_2, 2500);
    tft180_show_string_color(10, 70, "Max: 2500us", BLACK, WHITE);
    delay_ms(2000);
    
    // 校准完成
    servo_stop_all();
    tft180_show_string_color(10, 90, "Calibration Done!", BLACK, GREEN);
}
