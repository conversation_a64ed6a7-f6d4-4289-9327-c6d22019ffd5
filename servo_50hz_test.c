/*
 * 舵机50Hz频率测试程序
 * 验证舵机是否工作在标准50Hz频率下
 */

#include "common_inc.h"

// 50Hz舵机测试函数
void servo_50hz_test(void)
{
    // 初始化系统
    SYSCFG_DL_init();
    tft180_init();
    delay_ms(500);
    
    tft180_clear_color(BLACK);
    tft180_show_string_color(10, 10, "Servo 50Hz Test", BLACK, WHITE);
    tft180_show_string_color(10, 30, "Initializing...", BLACK, YELLOW);
    
    // 初始化舵机 - 这会配置PWM为50Hz
    servo_init();
    
    // 配置舵机类型
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    tft180_show_string_color(10, 30, "50Hz PWM Ready! ", BLAC<PERSON>, GRE<PERSON>);
    tft180_show_string_color(10, 50, "Period: 25600   ", BLAC<PERSON>, GRE<PERSON>);
    tft180_show_string_color(10, 70, "Freq: 50Hz      ", BLACK, GREEN);
    
    delay_ms(2000);
    
    // 测试序列
    uint32_t test_step = 0;
    uint32_t delay_counter = 0;
    
    while (1) {
        delay_counter++;
        
        // 每1000次循环切换一次位置（约1秒）
        if (delay_counter >= 1000) {
            delay_counter = 0;
            test_step = (test_step + 1) % 8;
            
            tft180_show_string_color(10, 90, "Test Step: ", BLACK, CYAN);
            tft180_show_num_color(100, 90, test_step, 1, 0, BLACK, CYAN);
            
            switch (test_step) {
                case 0:
                    // 两个舵机都到最小位置
                    servo_set_angle(SERVO_CHANNEL_1, 0.0f);
                    servo_set_angle(SERVO_CHANNEL_2, 0.0f);
                    tft180_show_string_color(10, 110, "Position: MIN   ", BLACK, WHITE);
                    break;
                    
                case 1:
                    // 舵机1到45度，舵机2到67.5度
                    servo_set_angle(SERVO_CHANNEL_1, 45.0f);
                    servo_set_angle(SERVO_CHANNEL_2, 67.5f);
                    tft180_show_string_color(10, 110, "Position: 1/4   ", BLACK, WHITE);
                    break;
                    
                case 2:
                    // 两个舵机都到中心位置
                    servo_set_angle(SERVO_CHANNEL_1, 90.0f);
                    servo_set_angle(SERVO_CHANNEL_2, 135.0f);
                    tft180_show_string_color(10, 110, "Position: CENTER", BLACK, WHITE);
                    break;
                    
                case 3:
                    // 舵机1到135度，舵机2到202.5度
                    servo_set_angle(SERVO_CHANNEL_1, 135.0f);
                    servo_set_angle(SERVO_CHANNEL_2, 202.5f);
                    tft180_show_string_color(10, 110, "Position: 3/4   ", BLACK, WHITE);
                    break;
                    
                case 4:
                    // 两个舵机都到最大位置
                    servo_set_angle(SERVO_CHANNEL_1, 180.0f);
                    servo_set_angle(SERVO_CHANNEL_2, 270.0f);
                    tft180_show_string_color(10, 110, "Position: MAX   ", BLACK, WHITE);
                    break;
                    
                case 5:
                    // 测试脉宽控制 - 0.5ms
                    servo_set_pulse_us(SERVO_CHANNEL_1, 500);
                    servo_set_pulse_us(SERVO_CHANNEL_2, 500);
                    tft180_show_string_color(10, 110, "Pulse: 0.5ms    ", BLACK, WHITE);
                    break;
                    
                case 6:
                    // 测试脉宽控制 - 1.5ms
                    servo_set_pulse_us(SERVO_CHANNEL_1, 1500);
                    servo_set_pulse_us(SERVO_CHANNEL_2, 1500);
                    tft180_show_string_color(10, 110, "Pulse: 1.5ms    ", BLACK, WHITE);
                    break;
                    
                case 7:
                    // 测试脉宽控制 - 2.5ms
                    servo_set_pulse_us(SERVO_CHANNEL_1, 2500);
                    servo_set_pulse_us(SERVO_CHANNEL_2, 2500);
                    tft180_show_string_color(10, 110, "Pulse: 2.5ms    ", BLACK, WHITE);
                    break;
            }
            
            // 显示当前角度
            float angle1 = servo_get_angle(SERVO_CHANNEL_1);
            float angle2 = servo_get_angle(SERVO_CHANNEL_2);
            
            tft180_show_string_color(10, 130, "Servo1: ", BLACK, GREEN);
            tft180_show_num_color(70, 130, angle1, 3, 1, BLACK, GREEN);
            tft180_show_string_color(120, 130, "deg", BLACK, GREEN);
            
            tft180_show_string_color(10, 150, "Servo2: ", BLACK, GREEN);
            tft180_show_num_color(70, 150, angle2, 3, 1, BLACK, GREEN);
            tft180_show_string_color(120, 150, "deg", BLACK, GREEN);
        }
        
        delay_ms(1);  // 1ms延时
    }
}

// 如果需要单独运行这个测试
#ifdef SERVO_50HZ_TEST_MAIN
int main(void)
{
    servo_50hz_test();
    return 0;
}
#endif
