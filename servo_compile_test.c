/*
 * 舵机库编译测试文件
 * 用于验证舵机库是否能正常编译
 */

#include "common_inc.h"

// 简单的舵机功能测试
void servo_compile_test(void)
{
    // 测试舵机初始化
    servo_init();
    
    // 测试舵机配置
    servo_config_type(SERVO_CHANNEL_1, SERVO_180_DEGREE);
    servo_config_type(SERVO_CHANNEL_2, SERVO_270_DEGREE);
    
    // 测试角度设置
    servo_set_angle(SERVO_CHANNEL_1, 90.0f);
    servo_set_angle(SERVO_CHANNEL_2, 135.0f);
    
    // 测试脉宽设置
    servo_set_pulse_us(SERVO_CHANNEL_1, 1500);
    servo_set_pulse_us(SERVO_CHANNEL_2, 1500);
    
    // 测试角度获取
    float angle1 = servo_get_angle(SERVO_CHANNEL_1);
    float angle2 = servo_get_angle(SERVO_CHANNEL_2);
    
    // 测试停止功能
    servo_stop(SERVO_CHANNEL_1);
    servo_stop_all();
    
    // 避免编译器警告
    (void)angle1;
    (void)angle2;
}

// 如果需要单独测试编译，可以使用这个main函数
#ifdef SERVO_COMPILE_TEST_MAIN
int main(void)
{
    SYSCFG_DL_init();
    servo_compile_test();
    return 0;
}
#endif
