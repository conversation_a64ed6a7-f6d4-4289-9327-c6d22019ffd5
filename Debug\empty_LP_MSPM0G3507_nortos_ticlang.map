******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 11:56:57 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000026ad


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00003538  00004ac8  R  X
  SRAM                  20200000   00004000  00000405  00003bfb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003538   00003538    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002bf8   00002bf8    r-x .text
  00002cb8    00002cb8    00000848   00000848    r-- .rodata
  00003500    00003500    00000038   00000038    r-- .cinit
20200000    20200000    00000208   00000000    rw-
  20200000    20200000    000001ed   00000000    rw- .bss
  202001f0    202001f0    00000018   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002bf8     
                  000000c0    000002b8     empty.o (.text.main)
                  00000378    00000288     tft180.o (.text.tft180_init)
                  00000600    0000020c     encoder.o (.text.encoder_exti_callback)
                  0000080c    00000204     openmv.o (.text.openmv_display_data)
                  00000a10    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000bfc    000001bc     tft180.o (.text.func_float_to_str)
                  00000db8    00000130     tft180.o (.text.tft180_show_char_color)
                  00000ee8    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00001014    0000010c     tft180.o (.text.tft180_show_num_color)
                  00001120    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001224    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000130c    000000d8     servo.o (.text.servo_set_angle)
                  000013e4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000014bc    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001590    000000bc     servo.o (.text.servo_init)
                  0000164c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  000016d8    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00001764    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000017f0    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001874    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  000018f6    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000018f8    00000080     servo.o (.text.servo_config_type)
                  00001978    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000019f4    00000078     openmv.o (.text.openmv_init)
                  00001a6c    00000078     tft180.o (.text.tft180_clear_color)
                  00001ae4    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001b58    00000074     delay.o (.text.delay_us)
                  00001bcc    0000006c     tft180.o (.text.tft180_set_region)
                  00001c38    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001ca0    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001d08    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001d6a    00000002     empty.o (.text.timerA_callback)
                  00001d6c    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001dce    00000062     tft180.o (.text.tft180_show_string_color)
                  00001e30    00000058     openmv.o (.text.openmv_reset_uart_state)
                  00001e88    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001ede    00000002     empty.o (.text.timerB_callback)
                  00001ee0    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001f34    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001f84    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001fd0    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002018    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00002060    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  000020a8    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  000020ec    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00002130    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00002170    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  000021b0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  000021f0    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002230    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000226c    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  000022a8    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000022e4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002320    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000235c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00002396    00000002     --HOLE-- [fill = 0]
                  00002398    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000023d2    00000002     --HOLE-- [fill = 0]
                  000023d4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  0000240c    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00002440    00000034     openmv.o (.text.openmv_analysis)
                  00002474    00000034     servo.o (.text.servo_get_angle)
                  000024a8    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  000024d8    00000030     tft180.o (.text.tft180_write_index)
                  00002508    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00002534    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00002560    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  0000258c    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  000025b8    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  000025e4    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000260c    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  00002634    00000028     timer.o (.text.TIMG8_IRQHandler)
                  0000265c    00000028     debug.o (.text.UART0_IRQHandler)
                  00002684    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000026ac    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000026d4    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  000026f8    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000271a    00000002     --HOLE-- [fill = 0]
                  0000271c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000273c    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000275a    00000002     --HOLE-- [fill = 0]
                  0000275c    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  00002778    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002794    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000027b0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000027cc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  000027e8    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002804    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002820    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  0000283c    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00002858    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002874    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00002890    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000028a8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000028c0    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  000028d8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000028f0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002908    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00002920    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00002938    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00002950    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  00002968    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00002980    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00002998    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  000029b0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000029c8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000029e0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000029f8    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002a10    00000018     servo.o (.text.DL_Timer_startCounter)
                  00002a28    00000018     servo.o (.text.DL_Timer_stopCounter)
                  00002a40    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00002a58    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  00002a70    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002a88    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002a9e    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002ab4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002aca    00000016     delay.o (.text.delay_ms)
                  00002ae0    00000016     timer.o (.text.timerA_init)
                  00002af6    00000016     timer.o (.text.timerB_init)
                  00002b0c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002b22    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002b36    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002b4a    00000002     --HOLE-- [fill = 0]
                  00002b4c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002b60    00000014     servo.o (.text.DL_Timer_enableClock)
                  00002b74    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002b88    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002b9c    00000014     debug.o (.text.DL_UART_receiveData)
                  00002bb0    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002bc4    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002bd6    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002be8    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00002bfa    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002c0c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002c1e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002c30    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002c42    00000002     --HOLE-- [fill = 0]
                  00002c44    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002c54    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002c64    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002c74    0000000c     timer.o (.text.get_system_time_ms)
                  00002c80    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002c8a    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002c92    00000002     --HOLE-- [fill = 0]
                  00002c94    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002c9c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002ca2    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002ca6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002caa    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002cae    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002cb2    00000006     --HOLE-- [fill = 0]

.cinit     0    00003500    00000038     
                  00003500    00000010     (.cinit..data.load) [load image, compression = lzss]
                  00003510    0000000c     (__TI_handler_table)
                  0000351c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003524    00000010     (__TI_cinit_table)
                  00003534    00000004     --HOLE-- [fill = 0]

.rodata    0    00002cb8    00000848     
                  00002cb8    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  000032a8    00000016     empty.o (.rodata.str1.4000995719088696555.1)
                  000032be    00000015     empty.o (.rodata.str1.15930989295766594416.1)
                  000032d3    00000015     empty.o (.rodata.str1.5100843677217179155.1)
                  000032e8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  000032fc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00003310    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00003324    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00003337    00000013     empty.o (.rodata.str1.6969777895189110550.1)
                  0000334a    00000013     empty.o (.rodata.str1.7079713434352825882.1)
                  0000335d    00000012     empty.o (.rodata.str1.10059295903439925133.1)
                  0000336f    00000012     empty.o (.rodata.str1.101204432782223354.1)
                  00003381    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00003393    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  000033a5    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  000033b7    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  000033c9    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  000033db    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  000033ed    00000012     empty.o (.rodata.str1.15486159998237592746.1)
                  000033ff    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00003411    00000012     empty.o (.rodata.str1.18230774644621384456.1)
                  00003423    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00003435    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00003447    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00003459    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  0000346b    0000000b     empty.o (.rodata.str1.7048729596307760267.1)
                  00003476    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00003480    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  0000348a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00003494    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  0000349e    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  000034a8    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  000034b2    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  000034bc    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  000034c4    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  000034cc    00000008     servo.o (.rodata.servo_init.servo_pwm_config)
                  000034d4    00000007     empty.o (.rodata.str1.17102900359786703537.1)
                  000034db    00000007     empty.o (.rodata.str1.17676052816137622666.1)
                  000034e2    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  000034e5    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  000034e8    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  000034eb    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  000034ee    00000003     servo.o (.rodata.servo_init.servo_clock_config)
                  000034f1    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  000034f4    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  000034f6    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  000034f8    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000034fa    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  000034fc    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  000034fe    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001ed     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000018     servo.o (.bss.servo_configs)
                  202001d8    0000000c     (.common:openmvData)
                  202001e4    00000008     openmv.o (.bss.rx_buffer)
                  202001ec    00000001     openmv.o (.bss.data)

.data      0    202001f0    00000018     UNINITIALIZED
                  202001f0    00000004     timer.o (.data.system_time_ms)
                  202001f4    00000002     encoder.o (.data.left_counter)
                  202001f6    00000002     encoder.o (.data.right_counter)
                  202001f8    00000002     empty.o (.data.tft180_bgcolor)
                  202001fa    00000002     openmv.o (.data.tft180_bgcolor)
                  202001fc    00000002     tft180.o (.data.tft180_bgcolor)
                  202001fe    00000002     empty.o (.data.tft180_pencolor)
                  20200200    00000002     openmv.o (.data.tft180_pencolor)
                  20200202    00000001     empty.o (.data.main.test_step)
                  20200203    00000001     openmv.o (.data.n)
                  20200204    00000001     openmv.o (.data.state)
                  20200205    00000001     tft180.o (.data.tft180_x_max)
                  20200206    00000001     tft180.o (.data.tft180_y_max)
                  20200207    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2814    128       448    
       empty.o                        708     219       5      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3530    539       453    
                                                               
    .\drivers\
       tft180.o                       2240    1520      4      
       openmv.o                       1306    240       27     
       servo.o                        652     11        24     
       encoder.o                      598     0         4      
       timer.o                        230     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         5026    1771      63     
                                                               
    .\soft\
       delay.o                        138     0         0      
       debug.o                        78      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         216     0         1      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         774     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         292     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1394    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       52        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   11236   2362      1029   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003524 records: 2, size/record: 8, table size: 16
	.data: load addr=00003500, load size=00000010 bytes, run addr=202001f0, run size=00000018 bytes, compression=lzss
	.bss: load addr=0000351c, load size=00000008 bytes, run addr=20200000, run size=000001ed bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003510 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002ca3  ADC0_IRQHandler                 
00002ca3  ADC1_IRQHandler                 
00002ca3  AES_IRQHandler                  
00002ca6  C$$EXIT                         
00002ca3  CANFD0_IRQHandler               
00002ca3  DAC0_IRQHandler                 
00002c81  DL_Common_delayCycles           
000020a9  DL_SPI_init                     
00002bc5  DL_SPI_setClockConfig           
00001121  DL_Timer_initFourCCPWMMode      
00001225  DL_Timer_initTimerMode          
00002821  DL_Timer_setCaptCompUpdateMethod
000029f9  DL_Timer_setCaptureCompareOutCtl
00002c55  DL_Timer_setCaptureCompareValue 
0000283d  DL_Timer_setClockConfig         
00001fd1  DL_UART_init                    
00002c0d  DL_UART_setClockConfig          
00002ca3  DMA_IRQHandler                  
00002ca3  Default_Handler                 
00002ca3  GROUP0_IRQHandler               
00002c8b  GROUP1_IRQHandler               
00002ca7  HOSTexit                        
00002ca3  HardFault_Handler               
00002ca3  I2C0_IRQHandler                 
00002ca3  I2C1_IRQHandler                 
00002ca3  NMI_Handler                     
00002ca3  PendSV_Handler                  
00002ca3  RTC_IRQHandler                  
00002cab  Reset_Handler                   
00002ca3  SPI0_IRQHandler                 
00002ca3  SPI1_IRQHandler                 
00002ca3  SVC_Handler                     
00000a11  SYSCFG_DL_GPIO_init             
0000164d  SYSCFG_DL_PWM_6_init            
000016d9  SYSCFG_DL_PWM_7_init            
00002131  SYSCFG_DL_SPI_IMU660RB_init     
000026f9  SYSCFG_DL_SYSCTL_init           
00002c65  SYSCFG_DL_SYSTICK_init          
00002171  SYSCFG_DL_TFT_SPI_init          
0000226d  SYSCFG_DL_TIMER_12_init         
000021b1  SYSCFG_DL_TIMER_8_init          
00002019  SYSCFG_DL_UART_0_init           
00001ee1  SYSCFG_DL_UART_1_init           
00002061  SYSCFG_DL_UART_3_init           
00001c39  SYSCFG_DL_init                  
000014bd  SYSCFG_DL_initPower             
00002ca3  SysTick_Handler                 
00002ca3  TIMA0_IRQHandler                
00002ca3  TIMA1_IRQHandler                
00002ca3  TIMG0_IRQHandler                
00002875  TIMG12_IRQHandler               
00002ca3  TIMG6_IRQHandler                
00002ca3  TIMG7_IRQHandler                
00002635  TIMG8_IRQHandler                
00002c1f  TI_memcpy_small                 
0000265d  UART0_IRQHandler                
00002ca3  UART1_IRQHandler                
00002ca3  UART2_IRQHandler                
00000ee9  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00003524  __TI_CINIT_Base                 
00003534  __TI_CINIT_Limit                
00003534  __TI_CINIT_Warm                 
00003510  __TI_Handler_Table_Base         
0000351c  __TI_Handler_Table_Limit        
00002321  __TI_auto_init_nobinit_nopinit  
00001979  __TI_decompress_lzss            
00002c31  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002b0d  __TI_zero_init_nomemset         
000013ef  __addsf3                        
00001d09  __aeabi_dcmpeq                  
00001d45  __aeabi_dcmpge                  
00001d59  __aeabi_dcmpgt                  
00001d31  __aeabi_dcmple                  
00001d1d  __aeabi_dcmplt                  
000021f1  __aeabi_f2d                     
000023d5  __aeabi_f2iz                    
000013ef  __aeabi_fadd                    
00001d6d  __aeabi_fcmpeq                  
00001da9  __aeabi_fcmpge                  
00001dbd  __aeabi_fcmpgt                  
00001d95  __aeabi_fcmple                  
00001d81  __aeabi_fcmplt                  
00001875  __aeabi_fdiv                    
00001765  __aeabi_fmul                    
000013e5  __aeabi_fsub                    
000022a9  __aeabi_i2f                     
00001e89  __aeabi_idiv                    
000018f7  __aeabi_idiv0                   
00001e89  __aeabi_idivmod                 
00002c95  __aeabi_memcpy                  
00002c95  __aeabi_memcpy4                 
00002c95  __aeabi_memcpy8                 
00002685  __aeabi_ui2f                    
ffffffff  __binit__                       
00001ca1  __cmpdf2                        
0000235d  __cmpsf2                        
00001875  __divsf3                        
00001ca1  __eqdf2                         
0000235d  __eqsf2                         
000021f1  __extendsfdf2                   
000023d5  __fixsfsi                       
000022a9  __floatsisf                     
00002685  __floatunsisf                   
00001ae5  __gedf2                         
000022e5  __gesf2                         
00001ae5  __gtdf2                         
000022e5  __gtsf2                         
00001ca1  __ledf2                         
0000235d  __lesf2                         
00001ca1  __ltdf2                         
0000235d  __ltsf2                         
UNDEFED   __mpu_init                      
00002399  __muldsi3                       
00001765  __mulsf3                        
00001ca1  __nedf2                         
0000235d  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000013e5  __subsf3                        
000026ad  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00002caf  _system_pre_init                
00002c9d  abort                           
00002cb8  ascii_font_8x16                 
ffffffff  binit                           
00002acb  delay_ms                        
00001b59  delay_us                        
00000601  encoder_exti_callback           
00000bfd  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002c75  get_system_time_ms              
00000000  interruptVectors                
202001f4  left_counter                    
000000c1  main                            
202001d8  openmvData                      
00002441  openmv_analysis                 
0000080d  openmv_display_data             
000019f5  openmv_init                     
00001f35  openmv_is_data_valid            
00001e31  openmv_reset_uart_state         
202001f6  right_counter                   
000018f9  servo_config_type               
00002475  servo_get_angle                 
00001591  servo_init                      
0000130d  servo_set_angle                 
00001a6d  tft180_clear_color              
00000379  tft180_init                     
00000db9  tft180_show_char_color          
00001015  tft180_show_num_color           
00001dcf  tft180_show_string_color        
000020ed  tft180_write_16bit_data         
000025b9  tft180_write_8bit_data          
00001d6b  timerA_callback                 
00002ae1  timerA_init                     
00001edf  timerB_callback                 
00002af7  timerB_init                     
20200207  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  main                            
00000200  __STACK_SIZE                    
00000379  tft180_init                     
00000601  encoder_exti_callback           
0000080d  openmv_display_data             
00000a11  SYSCFG_DL_GPIO_init             
00000bfd  func_float_to_str               
00000db9  tft180_show_char_color          
00000ee9  UART3_IRQHandler                
00001015  tft180_show_num_color           
00001121  DL_Timer_initFourCCPWMMode      
00001225  DL_Timer_initTimerMode          
0000130d  servo_set_angle                 
000013e5  __aeabi_fsub                    
000013e5  __subsf3                        
000013ef  __addsf3                        
000013ef  __aeabi_fadd                    
000014bd  SYSCFG_DL_initPower             
00001591  servo_init                      
0000164d  SYSCFG_DL_PWM_6_init            
000016d9  SYSCFG_DL_PWM_7_init            
00001765  __aeabi_fmul                    
00001765  __mulsf3                        
00001875  __aeabi_fdiv                    
00001875  __divsf3                        
000018f7  __aeabi_idiv0                   
000018f9  servo_config_type               
00001979  __TI_decompress_lzss            
000019f5  openmv_init                     
00001a6d  tft180_clear_color              
00001ae5  __gedf2                         
00001ae5  __gtdf2                         
00001b59  delay_us                        
00001c39  SYSCFG_DL_init                  
00001ca1  __cmpdf2                        
00001ca1  __eqdf2                         
00001ca1  __ledf2                         
00001ca1  __ltdf2                         
00001ca1  __nedf2                         
00001d09  __aeabi_dcmpeq                  
00001d1d  __aeabi_dcmplt                  
00001d31  __aeabi_dcmple                  
00001d45  __aeabi_dcmpge                  
00001d59  __aeabi_dcmpgt                  
00001d6b  timerA_callback                 
00001d6d  __aeabi_fcmpeq                  
00001d81  __aeabi_fcmplt                  
00001d95  __aeabi_fcmple                  
00001da9  __aeabi_fcmpge                  
00001dbd  __aeabi_fcmpgt                  
00001dcf  tft180_show_string_color        
00001e31  openmv_reset_uart_state         
00001e89  __aeabi_idiv                    
00001e89  __aeabi_idivmod                 
00001edf  timerB_callback                 
00001ee1  SYSCFG_DL_UART_1_init           
00001f35  openmv_is_data_valid            
00001fd1  DL_UART_init                    
00002019  SYSCFG_DL_UART_0_init           
00002061  SYSCFG_DL_UART_3_init           
000020a9  DL_SPI_init                     
000020ed  tft180_write_16bit_data         
00002131  SYSCFG_DL_SPI_IMU660RB_init     
00002171  SYSCFG_DL_TFT_SPI_init          
000021b1  SYSCFG_DL_TIMER_8_init          
000021f1  __aeabi_f2d                     
000021f1  __extendsfdf2                   
0000226d  SYSCFG_DL_TIMER_12_init         
000022a9  __aeabi_i2f                     
000022a9  __floatsisf                     
000022e5  __gesf2                         
000022e5  __gtsf2                         
00002321  __TI_auto_init_nobinit_nopinit  
0000235d  __cmpsf2                        
0000235d  __eqsf2                         
0000235d  __lesf2                         
0000235d  __ltsf2                         
0000235d  __nesf2                         
00002399  __muldsi3                       
000023d5  __aeabi_f2iz                    
000023d5  __fixsfsi                       
00002441  openmv_analysis                 
00002475  servo_get_angle                 
000025b9  tft180_write_8bit_data          
00002635  TIMG8_IRQHandler                
0000265d  UART0_IRQHandler                
00002685  __aeabi_ui2f                    
00002685  __floatunsisf                   
000026ad  _c_int00_noargs                 
000026f9  SYSCFG_DL_SYSCTL_init           
00002821  DL_Timer_setCaptCompUpdateMethod
0000283d  DL_Timer_setClockConfig         
00002875  TIMG12_IRQHandler               
000029f9  DL_Timer_setCaptureCompareOutCtl
00002acb  delay_ms                        
00002ae1  timerA_init                     
00002af7  timerB_init                     
00002b0d  __TI_zero_init_nomemset         
00002bc5  DL_SPI_setClockConfig           
00002c0d  DL_UART_setClockConfig          
00002c1f  TI_memcpy_small                 
00002c31  __TI_decompress_none            
00002c55  DL_Timer_setCaptureCompareValue 
00002c65  SYSCFG_DL_SYSTICK_init          
00002c75  get_system_time_ms              
00002c81  DL_Common_delayCycles           
00002c8b  GROUP1_IRQHandler               
00002c95  __aeabi_memcpy                  
00002c95  __aeabi_memcpy4                 
00002c95  __aeabi_memcpy8                 
00002c9d  abort                           
00002ca3  ADC0_IRQHandler                 
00002ca3  ADC1_IRQHandler                 
00002ca3  AES_IRQHandler                  
00002ca3  CANFD0_IRQHandler               
00002ca3  DAC0_IRQHandler                 
00002ca3  DMA_IRQHandler                  
00002ca3  Default_Handler                 
00002ca3  GROUP0_IRQHandler               
00002ca3  HardFault_Handler               
00002ca3  I2C0_IRQHandler                 
00002ca3  I2C1_IRQHandler                 
00002ca3  NMI_Handler                     
00002ca3  PendSV_Handler                  
00002ca3  RTC_IRQHandler                  
00002ca3  SPI0_IRQHandler                 
00002ca3  SPI1_IRQHandler                 
00002ca3  SVC_Handler                     
00002ca3  SysTick_Handler                 
00002ca3  TIMA0_IRQHandler                
00002ca3  TIMA1_IRQHandler                
00002ca3  TIMG0_IRQHandler                
00002ca3  TIMG6_IRQHandler                
00002ca3  TIMG7_IRQHandler                
00002ca3  UART1_IRQHandler                
00002ca3  UART2_IRQHandler                
00002ca6  C$$EXIT                         
00002ca7  HOSTexit                        
00002cab  Reset_Handler                   
00002caf  _system_pre_init                
00002cb8  ascii_font_8x16                 
00003510  __TI_Handler_Table_Base         
0000351c  __TI_Handler_Table_Limit        
00003524  __TI_CINIT_Base                 
00003534  __TI_CINIT_Limit                
00003534  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001d8  openmvData                      
202001f4  left_counter                    
202001f6  right_counter                   
20200207  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[179 symbols]
